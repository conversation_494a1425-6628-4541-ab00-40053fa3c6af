'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/lib/hooks/useAuth'
import { useSelectedOrganization } from '@/lib/stores/organizationStore'
import { DomainConfig } from '@/lib/types/domain'
import { Eye, Globe, Plus, Settings } from 'lucide-react'
import { useEffect, useState } from 'react'
import { DomainForm } from './components/domain-form'
import { DomainPreview } from './components/domain-preview'

export default function DomainsPage() {
  const [domains, setDomains] = useState<DomainConfig[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const { user } = useAuth()
  const selectedOrganization = useSelectedOrganization()
  const [showForm, setShowForm] = useState(false)
  const [selectedDomain, setSelectedDomain] = useState<DomainConfig | null>(null)
  const [showPreview, setShowPreview] = useState(false)

  useEffect(() => {
    fetchDomains()
  }, [])

  const fetchDomains = async () => {
    try {
      setIsLoading(true)

      // Get organization ID from selected organization or current user organization
      const organizationId = selectedOrganization?.id || user?.current_organization?.id

      if (!organizationId) {
        console.error('No organization context available')
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/${organizationId}/domains`, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          Accept: 'application/json',
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setDomains(data.data)
        }
      }
    } catch (error) {
      console.error('Error fetching domains:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateDomain = () => {
    setSelectedDomain(null)
    setShowForm(true)
  }

  const handleEditDomain = (domain: DomainConfig) => {
    setSelectedDomain(domain)
    setShowForm(true)
  }

  const handlePreviewDomain = (domain: DomainConfig) => {
    setSelectedDomain(domain)
    setShowPreview(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setSelectedDomain(null)
    fetchDomains() // Refresh the list
  }

  const handlePreviewClose = () => {
    setShowPreview(false)
    setSelectedDomain(null)
  }

  const handleSetDefault = async (domainId: string) => {
    try {
      // Get organization ID from selected organization or current user organization
      const organizationId = selectedOrganization?.id || user?.current_organization?.id

      if (!organizationId) {
        console.error('No organization context available')
        return
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/${organizationId}/domains/${domainId}/set-default`,
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('token')}`,
            Accept: 'application/json',
          },
        }
      )

      if (response.ok) {
        fetchDomains() // Refresh the list
      }
    } catch (error) {
      console.error('Error setting default domain:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex h-64 items-center justify-center">
          <div className="text-center">
            <div className="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"></div>
            <p className="text-muted-foreground mt-2">Đang tải domains...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto space-y-6 py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý Domains</h1>
          <p className="text-muted-foreground">Quản lý các domain và cấu hình đặc thù cho từng domain</p>
        </div>
        <Button onClick={handleCreateDomain}>
          <Plus className="mr-2 h-4 w-4" />
          Thêm Domain
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {domains.map((domain) => (
          <Card
            key={domain.id}
            className="relative">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="text-primary h-5 w-5" />
                  <CardTitle className="text-lg">{domain.name}</CardTitle>
                </div>
                <div className="flex gap-1">
                  {domain.custom?.is_default && <Badge variant="default">Mặc định</Badge>}
                  <Badge variant={domain.custom?.is_active ? 'secondary' : 'destructive'}>
                    {domain.custom?.is_active ? 'Hoạt động' : 'Tạm dừng'}
                  </Badge>
                </div>
              </div>
              <CardDescription>
                <div className="space-y-1">
                  <p className="font-mono text-sm">{domain.hostname}</p>
                  {domain.description && <p className="text-xs">{domain.description}</p>}
                </div>
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="space-y-4">
                {/* Branding Info */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Thương hiệu</h4>
                  <div className="flex items-center gap-2">
                    {domain.branding?.logo_url ? (
                      <img
                        src={domain.branding.logo_url}
                        alt={domain.branding.name}
                        className="h-6 w-6 object-contain"
                      />
                    ) : (
                      <div className="bg-primary text-primary-foreground flex h-6 w-6 items-center justify-center rounded text-xs">
                        {domain.branding?.name?.charAt(0) || 'A'}
                      </div>
                    )}
                    <span className="text-sm">{domain.branding?.name || domain.name}</span>
                  </div>
                  {domain.branding?.slogan && (
                    <p className="text-muted-foreground text-xs italic">"{domain.branding.slogan}"</p>
                  )}
                </div>

                {/* Theme Colors */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Màu chủ đạo</h4>
                  <div className="flex gap-1">
                    <div
                      className="h-4 w-4 rounded border"
                      style={{ backgroundColor: domain.theme?.primary }}
                      title={`Primary: ${domain.theme?.primary}`}
                    />
                    <div
                      className="h-4 w-4 rounded border"
                      style={{ backgroundColor: domain.theme?.secondary }}
                      title={`Secondary: ${domain.theme?.secondary}`}
                    />
                    <div
                      className="h-4 w-4 rounded border"
                      style={{ backgroundColor: domain.theme?.accent }}
                      title={`Accent: ${domain.theme?.accent}`}
                    />
                  </div>
                </div>

                {/* Features */}
                {domain.features && domain.features.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Tính năng</h4>
                    <div className="flex flex-wrap gap-1">
                      {domain.features.slice(0, 3).map((feature) => (
                        <Badge
                          key={feature}
                          variant="outline"
                          className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {domain.features.length > 3 && (
                        <Badge
                          variant="outline"
                          className="text-xs">
                          +{domain.features.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePreviewDomain(domain)}>
                    <Eye className="mr-1 h-3 w-3" />
                    Xem
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditDomain(domain)}>
                    <Settings className="mr-1 h-3 w-3" />
                    Sửa
                  </Button>
                  {!domain.custom?.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefault(domain.id)}>
                      Đặt mặc định
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {domains.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Globe className="text-muted-foreground mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-medium">Chưa có domain nào</h3>
            <p className="text-muted-foreground mb-4 text-center">
              Tạo domain đầu tiên để bắt đầu cấu hình multi-domain cho ứng dụng
            </p>
            <Button onClick={handleCreateDomain}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm Domain Đầu Tiên
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Domain Form Modal */}
      {showForm && (
        <DomainForm
          domain={selectedDomain}
          onClose={handleFormClose}
        />
      )}

      {/* Domain Preview Modal */}
      {showPreview && selectedDomain && (
        <DomainPreview
          domain={selectedDomain}
          onClose={handlePreviewClose}
        />
      )}
    </div>
  )
}
