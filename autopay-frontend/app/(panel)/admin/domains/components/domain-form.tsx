'use client'

import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { useAuth } from '@/lib/hooks/useAuth'
import { useSelectedOrganization } from '@/lib/stores/organizationStore'
import { DomainConfig } from '@/lib/types/domain'
import { Save, X } from 'lucide-react'
import { useEffect, useState } from 'react'

interface DomainFormProps {
  domain?: DomainConfig | null
  onClose: () => void
}

export function DomainForm({ domain, onClose }: DomainFormProps) {
  const { user } = useAuth()
  const selectedOrganization = useSelectedOrganization()
  const [formData, setFormData] = useState({
    hostname: '',
    name: '',
    description: '',
    brand_name: '',
    slogan: '',
    logo_url: '',
    favicon_url: '',
    theme_colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      text_secondary: '#64748b',
    },
    meta_title: '',
    meta_description: '',
    meta_keywords: '',
    og_image_url: '',
    enabled_features: [] as string[],
    default_locale: 'vi',
    supported_locales: ['vi', 'en'],
    contact_info: {},
    custom_config: {},
    is_active: true,
    is_default: false,
    force_https: true,
  })

  const [isLoading, setIsLoading] = useState(false)
  const [newFeature, setNewFeature] = useState('')

  useEffect(() => {
    if (domain) {
      setFormData({
        hostname: domain.hostname || '',
        name: domain.name || '',
        description: domain.description || '',
        brand_name: domain.branding?.name || '',
        slogan: domain.branding?.slogan || '',
        logo_url: domain.branding?.logo_url || '',
        favicon_url: domain.branding?.favicon_url || '',
        theme_colors: {
          primary: domain.theme?.primary || '#3b82f6',
          secondary: domain.theme?.secondary || '#64748b',
          accent: domain.theme?.accent || '#f59e0b',
          background: domain.theme?.background || '#ffffff',
          surface: domain.theme?.surface || '#f8fafc',
          text: domain.theme?.text || '#1e293b',
          text_secondary: domain.theme?.text_secondary || '#64748b',
        },
        meta_title: domain.seo?.title || '',
        meta_description: domain.seo?.description || '',
        meta_keywords: domain.seo?.keywords || '',
        og_image_url: domain.seo?.og_image || '',
        enabled_features: domain.features || [],
        default_locale: domain.locale?.default || 'vi',
        supported_locales: domain.locale?.supported || ['vi', 'en'],
        contact_info: domain.contact || {},
        custom_config: domain.custom || {},
        is_active: domain.custom?.is_active !== false,
        is_default: domain.custom?.is_default || false,
        force_https: domain.custom?.force_https !== false,
      })
    }
  }, [domain])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const payload = {
        hostname: formData.hostname,
        name: formData.name,
        description: formData.description,
        brand_name: formData.brand_name,
        slogan: formData.slogan,
        logo_url: formData.logo_url,
        favicon_url: formData.favicon_url,
        theme_colors: formData.theme_colors,
        meta_title: formData.meta_title,
        meta_description: formData.meta_description,
        meta_keywords: formData.meta_keywords,
        og_image_url: formData.og_image_url,
        enabled_features: formData.enabled_features,
        default_locale: formData.default_locale,
        supported_locales: formData.supported_locales,
        contact_info: formData.contact_info,
        custom_config: {
          ...formData.custom_config,
          is_active: formData.is_active,
          is_default: formData.is_default,
          force_https: formData.force_https,
        },
        is_active: formData.is_active,
        is_default: formData.is_default,
        force_https: formData.force_https,
      }

      // Get organization ID from selected organization or current user organization
      const organizationId = selectedOrganization?.id || user?.current_organization?.id

      if (!organizationId) {
        console.error('No organization context available')
        return
      }

      const url = domain
        ? `${process.env.NEXT_PUBLIC_API_URL}/api/${organizationId}/domains/${domain.id}`
        : `${process.env.NEXT_PUBLIC_API_URL}/api/${organizationId}/domains`

      const method = domain ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (response.ok) {
        onClose()
      } else {
        const errorData = await response.json()
        console.error('Error saving domain:', errorData)
      }
    } catch (error) {
      console.error('Error saving domain:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddFeature = () => {
    if (newFeature.trim() && !formData.enabled_features.includes(newFeature.trim())) {
      setFormData((prev) => ({
        ...prev,
        enabled_features: [...prev.enabled_features, newFeature.trim()],
      }))
      setNewFeature('')
    }
  }

  const handleRemoveFeature = (feature: string) => {
    setFormData((prev) => ({
      ...prev,
      enabled_features: prev.enabled_features.filter((f) => f !== feature),
    }))
  }

  return (
    <Dialog
      open={true}
      onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {domain ? 'Chỉnh sửa Domain' : 'Thêm Domain Mới'}
          </DialogTitle>
        </DialogHeader>

        <form
          onSubmit={handleSubmit}
          className="space-y-6">
          <Tabs
            defaultValue="basic"
            className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Cơ bản</TabsTrigger>
              <TabsTrigger value="branding">Thương hiệu</TabsTrigger>
              <TabsTrigger value="theme">Giao diện</TabsTrigger>
              <TabsTrigger value="advanced">Nâng cao</TabsTrigger>
            </TabsList>

            <TabsContent
              value="basic"
              className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hostname">Hostname *</Label>
                  <Input
                    id="hostname"
                    value={formData.hostname}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        hostname: e.target.value,
                      }))
                    }
                    placeholder="app.autopay.vn"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Tên hiển thị *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData((prev) => ({ ...prev, name: e.target.value }))}
                    placeholder="AutoPAY App"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Mô tả</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Mô tả về domain này..."
                />
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={formData.is_active}
                    onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, is_active: checked }))}
                  />
                  <Label htmlFor="is_active">Kích hoạt</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, is_default: checked }))}
                  />
                  <Label htmlFor="is_default">Mặc định</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="force_https"
                    checked={formData.force_https}
                    onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, force_https: checked }))}
                  />
                  <Label htmlFor="force_https">Bắt buộc HTTPS</Label>
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="branding"
              className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="brand_name">Tên thương hiệu</Label>
                  <Input
                    id="brand_name"
                    value={formData.brand_name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        brand_name: e.target.value,
                      }))
                    }
                    placeholder="AutoPAY"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slogan">Slogan</Label>
                  <Input
                    id="slogan"
                    value={formData.slogan}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        slogan: e.target.value,
                      }))
                    }
                    placeholder="Thanh toán tự động thông minh"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="logo_url">URL Logo</Label>
                  <Input
                    id="logo_url"
                    value={formData.logo_url}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        logo_url: e.target.value,
                      }))
                    }
                    placeholder="https://example.com/logo.png"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="favicon_url">URL Favicon</Label>
                  <Input
                    id="favicon_url"
                    value={formData.favicon_url}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        favicon_url: e.target.value,
                      }))
                    }
                    placeholder="https://example.com/favicon.ico"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent
              value="theme"
              className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(formData.theme_colors).map(([key, value]) => (
                  <div
                    key={key}
                    className="space-y-2">
                    <Label
                      htmlFor={key}
                      className="capitalize">
                      {key.replace('_', ' ')}
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        id={key}
                        type="color"
                        value={value}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            theme_colors: {
                              ...prev.theme_colors,
                              [key]: e.target.value,
                            },
                          }))
                        }
                        className="h-10 w-16"
                      />
                      <Input
                        value={value}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            theme_colors: {
                              ...prev.theme_colors,
                              [key]: e.target.value,
                            },
                          }))
                        }
                        placeholder="#000000"
                        className="flex-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent
              value="advanced"
              className="space-y-4">
              {/* SEO Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">SEO</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="meta_title">Meta Title</Label>
                    <Input
                      id="meta_title"
                      value={formData.meta_title}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          meta_title: e.target.value,
                        }))
                      }
                      placeholder="AutoPAY - Thanh toán tự động"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="meta_description">Meta Description</Label>
                    <Textarea
                      id="meta_description"
                      value={formData.meta_description}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          meta_description: e.target.value,
                        }))
                      }
                      placeholder="Mô tả cho SEO..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="meta_keywords">Meta Keywords</Label>
                    <Input
                      id="meta_keywords"
                      value={formData.meta_keywords}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          meta_keywords: e.target.value,
                        }))
                      }
                      placeholder="thanh toán, tự động, autopay"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Tính năng</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      placeholder="Tên tính năng..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddFeature())}
                    />
                    <Button
                      type="button"
                      onClick={handleAddFeature}>
                      Thêm
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.enabled_features.map((feature) => (
                      <Badge
                        key={feature}
                        variant="secondary"
                        className="cursor-pointer">
                        {feature}
                        <X
                          className="ml-1 h-3 w-3"
                          onClick={() => handleRemoveFeature(feature)}
                        />
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-2 border-t pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}>
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isLoading}>
              {isLoading ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
              ) : (
                <Save className="mr-2 h-4 w-4" />
              )}
              {domain ? 'Cập nhật' : 'Tạo mới'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
