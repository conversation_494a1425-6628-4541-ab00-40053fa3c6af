<?php

namespace Modules\Core\Http\Requests;

class StoreDomainRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'hostname' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'brand_name' => 'nullable|string|max:255',
            'slogan' => 'nullable|string|max:500',
            'logo_url' => 'nullable|url',
            'favicon_url' => 'nullable|url',
            'theme_colors' => 'nullable|array',
            'custom_css' => 'nullable|array',
            'font_family' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'og_image_url' => 'nullable|url',
            'enabled_features' => 'nullable|array',
            'feature_configs' => 'nullable|array',
            'default_locale' => 'nullable|string|max:10',
            'supported_locales' => 'nullable|array',
            'contact_info' => 'nullable|array',
            'custom_config' => 'nullable|array',
            'is_active' => 'nullable|boolean',
            'is_default' => 'nullable|boolean',
            'force_https' => 'nullable|boolean',
            'redirect_to' => 'nullable|url',
            'organization_id' => 'nullable|exists:organizations,id',
        ];
    }
}
