<?php

use Illuminate\Support\Facades\Route;
use Modules\Core\Http\Controllers\DomainController;

// Public domain configuration endpoint
Route::get('/domains/config', [DomainController::class, 'getConfig']);

// Protected domain management routes
Route::middleware(['auth:sanctum'])->group(function () {
    Route::apiResource('domains', DomainController::class);
    Route::post('domains/{id}/set-default', [DomainController::class, 'setDefault']);
    Route::post('domains/{id}/set-primary', [DomainController::class, 'setPrimary']);
    Route::post('domains/setup', [DomainController::class, 'setupDomain']);
    Route::post('domains/{id}/verification', [DomainController::class, 'createVerification']);
    Route::get('domains/{id}/verification-instructions', [DomainController::class, 'getVerificationInstructions']);
    Route::post('domains/verify', [DomainController::class, 'verifyDomain']);
    Route::get('organizations/{organizationId}/domains', [DomainController::class, 'getByOrganization']);
});

Route::group([
    'prefix' => '{organization}/{team}',
    'middleware' => 'team.standard',
], function () {
    //
});
