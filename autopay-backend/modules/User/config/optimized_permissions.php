<?php

/**
 * Optimized Permissions Configuration for our system
 *
 * This file defines all permissions using the standardized naming convention:
 * {module}:{resource}:{action}[:{scope}]
 *
 * Guard: sanctum (to match Laravel's default authentication)
 */

return [

    // Guard configuration
    'guard' => 'sanctum',

    // ========================================
    // ORGANIZATION MODULE PERMISSIONS
    // ========================================

    'organization_management' => [
        'organization:instance:view' => 'View Organization',
        'organization:instance:update' => 'Update Organization',
        'organization:instance:admin' => 'Organization Administration',
        'organization:member:invite' => 'Invite Organization Members',
        'organization:member:remove' => 'Remove Organization Members',
        'organization:member:manage' => 'Manage Organization Members',
        'organization:settings:manage' => 'Manage Organization Settings',
        'organization:resource:assign' => 'Assign Resources to Teams (Organization Owner Only)',
        'organization:resource:unassign' => 'Unassign Resources from Teams (Organization Owner Only)',
    ],

    // ========================================
    // TEAM MODULE PERMISSIONS
    // ========================================

    'team_management' => [
        'team:instance:view' => 'View Teams',
        'team:instance:create' => 'Create Team',
        'team:instance:update' => 'Update Team',
        'team:instance:delete' => 'Delete Team',
        'team:member:invite' => 'Invite Team Members',
        'team:member:remove' => 'Remove Team Members',
        'team:member:manage' => 'Manage Team Members',
        'team:resource:assign' => 'Assign Resources to Team',
        'team:resource:unassign' => 'Unassign Resources from Team',
        'team:instance:admin' => 'Team Administration',
    ],

    // ========================================
    // ROLE MODULE PERMISSIONS
    // ========================================

    'role_management' => [
        'role:instance:view' => 'View Roles',
        'role:instance:create' => 'Create Role',
        'role:instance:update' => 'Update Role',
        'role:instance:delete' => 'Delete Role',
        'role:permission:assign' => 'Assign Permissions to Role',
        'role:permission:revoke' => 'Revoke Permissions from Role',
        'role:instance:manage' => 'Full Role Management',
    ],

    // ========================================
    // NOTIFICATION MODULE PERMISSIONS
    // ========================================

    'notification_management' => [
        'notification:instance:view' => 'View Notifications',
        'notification:instance:create' => 'Create Notification',
        'notification:instance:update' => 'Update Notification',
        'notification:instance:delete' => 'Delete Notification',
        'notification:instance:manage' => 'Manage Notifications',
    ],

    // ========================================
    // INTEGRATION PERMISSIONS
    // ========================================

    'integration_management' => [
        'integration:instance:view' => 'View Integrations',
        'integration:instance:create' => 'Add Integration',
        'integration:instance:update' => 'Update Integration',
        'integration:instance:delete' => 'Delete Integration',
        'integration:instance:manage' => 'Manage Integrations',
    ],

    // ========================================
    // BASE MODULE PERMISSIONS
    // ========================================

    'base_management' => [
        'base:instance:view' => 'View Base Instances',
        'base:instance:create' => 'Create Base Instance',
        'base:instance:update' => 'Update Base Instance',
        'base:instance:delete' => 'Delete Base Instance',
        'base:instance:manage' => 'Full Base Management',
    ],

    // ========================================
    // ACTIVITY MODULE PERMISSIONS
    // ========================================

    'activity_management' => [
        'activity:instance:view' => 'View Activity Logs',
        'activity:instance:delete' => 'Delete Activity Log',
        'activity:instance:manage' => 'Manage Activity Logs',
    ],

    // ========================================
    // WEBAPP MODULE PERMISSIONS
    // ========================================

    'webapp_domain' => [
        'webapp:domain:view' => 'View Domains',
        'webapp:domain:create' => 'Create Domain',
        'webapp:domain:update' => 'Update Domain',
        'webapp:domain:delete' => 'Delete Domain',
        'webapp:domain:manage' => 'Manage Domains',
    ],

    // ========================================
    // API PERMISSIONS
    // ========================================

    'api_management' => [
        'api:key:view' => 'View API Keys',
        'api:key:create' => 'Create API Key',
        'api:key:update' => 'Update API Key',
        'api:key:delete' => 'Delete API Key',
        'api:key:manage' => 'Manage API Keys',
    ],
];
